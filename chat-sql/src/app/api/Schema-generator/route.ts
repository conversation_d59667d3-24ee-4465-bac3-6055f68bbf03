import { NextRequest, NextResponse } from "next/server";
import {
  BailianAIRequest,
  BailianAIResponse,
  HTTPStatus,
} from "@/types/chatBotTypes/bailianai";
import {
  SchemaGeneratorResponse,
  AGENT_CONFIG,
} from "@/types/chatBotTypes/agents";
import * as agentsHandlers from "@/services/agentsHandlers";

/**
 * 解析智能体响应中的元数据
 */
function parseMetadata(text: string): { cleanText: string; metadata?: any } {
  try {
    // 尝试从响应文本中提取JSON格式的元数据
    const metadataRegex = /```json\s*(\{[\s\S]*?\})\s*```/;
    const match = text.match(metadataRegex);

    if (match) {
      const metadata = JSON.parse(match[1]);
      const cleanText = text.replace(metadataRegex, "").trim();
      return { cleanText, metadata };
    }

    return { cleanText: text };
  } catch (error) {
    console.warn("Failed to parse metadata from response:", error);
    return { cleanText: text };
  }
}

/**
 * 创建Schema-generator智能体请求
 */
function createSchemaGeneratorRequest(
  natural_language_query: string,
  sessionId?: string,
  parameters?: any,
): BailianAIRequest {
  const request: BailianAIRequest = {
    input: {
      prompt: natural_language_query,
      biz_params: {
        natural_language_query: natural_language_query,
      },
    },
    parameters: parameters || {},
    debug: {},
  };

  if (sessionId) {
    request.input.session_id = sessionId;
  }

  return request;
}

/**
 * POST /api/Schema-generator - 处理Schema生成请求
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log("接收到的请求体:", JSON.stringify(body, null, 2));

    // 支持两种格式：新格式（百炼AI标准）和旧格式（向后兼容）
    let natural_language_query: string;
    let sessionId: string | undefined;
    let parameters: any;

    if (body.input && body.input.biz_params) {
      // 新格式：百炼AI标准格式
      natural_language_query = body.input.biz_params.natural_language_query;
      sessionId = body.input.session_id;
      parameters = body.parameters;
    } else {
      // 旧格式：向后兼容
      natural_language_query = body.natural_language_query;
      sessionId = body.sessionId;
      parameters = body.parameters;
    }

    if (!natural_language_query || typeof natural_language_query !== "string") {
      console.log("错误: 自然语言查询内容无效:", natural_language_query);
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "INVALID_QUERY",
            message: "自然语言查询内容不能为空",
          },
        } as SchemaGeneratorResponse,
        { status: HTTPStatus.BAD_REQUEST },
      );
    }

    console.log("创建Schema-generator请求参数:", {
      natural_language_query,
      sessionId,
      parameters,
    });

    const bailianRequest = createSchemaGeneratorRequest(
      natural_language_query,
      sessionId,
      parameters,
    );

    const isStream = parameters?.stream === true;

    if (isStream) {
      // 流式响应
      const responseStream = await agentsHandlers.callBailianAPIStream(
        bailianRequest,
        AGENT_CONFIG.SCHEMA_GENERATOR.APP_ID,
      );

      return new Response(responseStream, {
        headers: {
          "Content-Type": "text/event-stream",
          "Cache-Control": "no-cache",
          Connection: "keep-alive",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type, Authorization",
        },
      });
    } else {
      // 非流式响应
      const response = (await agentsHandlers.callBailianAPI(
        bailianRequest,
        AGENT_CONFIG.SCHEMA_GENERATOR.APP_ID,
      )) as BailianAIResponse;
      const { cleanText } = parseMetadata(response.output.text);

      // 构建基于类型数组的输出格式
      const outputParts: import("@/types/chatBotTypes/agents").AgentOutputPart[] =
        [];
      if (cleanText) {
        outputParts.push({
          type: "sql" as const,
          content: cleanText,
          metadata: {
            language: "sql",
          },
        });
      }

      const schemaResponse: SchemaGeneratorResponse = {
        success: true,
        data: {
          output: outputParts,
          sessionId: response.output.session_id,
        },
        usage: response.usage
          ? {
              inputTokens: response.usage.input_tokens,
              outputTokens: response.usage.output_tokens,
              totalTokens: response.usage.total_tokens,
            }
          : undefined,
      };

      return NextResponse.json(schemaResponse);
    }
  } catch (error) {
    console.error("Schema-generator API error:", error);

    const apiError = agentsHandlers.handleAPIError(error);
    const errorResponse: SchemaGeneratorResponse = {
      success: false,
      error: {
        code: apiError.code || apiError.type,
        message: apiError.message,
      },
    };

    const statusCode = apiError.statusCode || HTTPStatus.INTERNAL_SERVER_ERROR;
    return NextResponse.json(errorResponse, { status: statusCode });
  }
}
