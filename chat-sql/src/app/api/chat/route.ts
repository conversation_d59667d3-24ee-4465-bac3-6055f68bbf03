import { NextRequest, NextResponse } from "next/server";
import {
  BailianAIRequest,
  BailianAIResponse,
  ChatRequest,
  ChatResponse,
  BailianAIAPIError,
  ErrorType,
  HTTPStatus,
} from "@/types/chatBotTypes/bailianai";
import {
  AGENT_CONFIG,
  AgentOutputPart,
} from "@/types/chatBotTypes/agents";
import * as agentsHandlers from "@/services/agentsHandlers";

/**
 * 解析智能体响应中的元数据
 */
function parseMetadata(text: string): { cleanText: string; metadata?: any } {
  try {
    // 尝试从响应文本中提取JSON格式的元数据
    const metadataRegex = /```json\s*(\{[\s\S]*?\})\s*```/;
    const match = text.match(metadataRegex);

    if (match) {
      const metadata = JSON.parse(match[1]);
      const cleanText = text.replace(metadataRegex, "").trim();
      return { cleanText, metadata };
    }

    return { cleanText: text };
  } catch (error) {
    console.warn("Failed to parse metadata from response:", error);
    return { cleanText: text };
  }
}

/**
 * 创建聊天智能体请求
 */
function createChatRequest(
  message: string,
  sessionId?: string,
  parameters?: any,
): BailianAIRequest {
  const request: BailianAIRequest = {
    input: {
      prompt: message,
      biz_params: {
        message: message,
      },
    },
    parameters: {
      incremental_output: parameters?.stream ? true : false,
      ...parameters,
    },
    debug: {},
  };

  if (sessionId) {
    request.input.session_id = sessionId;
  }

  return request;
}







/**
 * POST /api/chat - 处理聊天请求
 */
export async function POST(req: NextRequest) {
  try {
    const body: any = await req.json();
    console.log("接收到的请求体:", JSON.stringify(body, null, 2));

    // 解析请求参数 - 支持新格式和旧格式
    let message: string;
    let sessionId: string | undefined;
    let parameters: any;

    if (body.input && body.input.biz_params) {
      // 新格式：百炼AI标准格式
      message = body.input.biz_params.message;
      sessionId = body.input.session_id;
      parameters = body.parameters;
    } else {
      // 旧格式：向后兼容
      message = body.message;
      sessionId = body.sessionId;
      parameters = body.parameters;
    }

    // 验证必需参数
    if (!message || typeof message !== "string") {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "INVALID_MESSAGE",
            message: "消息内容不能为空",
          },
        } as ChatResponse,
        { status: HTTPStatus.BAD_REQUEST },
      );
    }

    console.log("创建聊天请求参数:", {
      message,
      sessionId,
      parameters,
    });

    const chatRequest = createChatRequest(
      message,
      sessionId,
      parameters,
    );

    const isStream = parameters?.stream === true;

    if (isStream) {
      // 流式响应
      const responseStream = await agentsHandlers.callBailianAPIStream(
        chatRequest,
        AGENT_CONFIG.CHAT.APP_ID,
      );

      return new Response(responseStream, {
        headers: {
          "Content-Type": "text/event-stream",
          "Cache-Control": "no-cache",
          Connection: "keep-alive",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type, Authorization",
        },
      });
    } else {
      // 非流式响应
      const response = (await agentsHandlers.callBailianAPI(
        chatRequest,
        AGENT_CONFIG.CHAT.APP_ID,
      )) as BailianAIResponse;

      const { cleanText, metadata } = parseMetadata(response.output.text);

      // 构建基于类型数组的输出格式
      const outputParts: AgentOutputPart[] = [];

      if (cleanText) {
        outputParts.push({
          type: "text",
          content: cleanText,
        });
      }

      const chatResponse: ChatResponse = {
        success: true,
        data: {
          output: outputParts,
          sessionId: response.output.session_id,
          metadata: {
            module: "chat",
            ...metadata,
          },
        },
        usage: response.usage
          ? {
              inputTokens: response.usage.input_tokens,
              outputTokens: response.usage.output_tokens,
              totalTokens: response.usage.total_tokens,
            }
          : undefined,
      };

      return NextResponse.json(chatResponse);
    }
  } catch (error) {
    console.error("Chat API error:", error);

    const { response: errorResponse, statusCode } = agentsHandlers.createErrorResponse(
      error,
      class { success!: boolean; error!: { code: string; message: string } }
    );

    return NextResponse.json(errorResponse, { status: statusCode });
  }
}
